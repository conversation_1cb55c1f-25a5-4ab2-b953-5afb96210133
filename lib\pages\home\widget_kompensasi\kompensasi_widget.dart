import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/components/pdl_button.dart';
import 'package:pdl_superapp/controllers/widget/kompensasi_controller.dart';
import 'package:pdl_superapp/models/kompensasi_item_model.dart';
import 'package:pdl_superapp/routes/app_routes.dart';
import 'package:pdl_superapp/utils/constants.dart';

class KompensasiWidget extends StatelessWidget {
  KompensasiWidget({super.key});

  // Use GetX controller
  final KompensasiController controller = Get.put(KompensasiController());

  // Build a table row from a KompensasiItemModel
  TableRow _buildTableRow(KompensasiItemModel item) {
    bool showing = controller.isShowingField(item.label);
    return TableRow(
      children: [
        (showing)
            ? Padding(
              padding: const EdgeInsets.only(top: paddingSmall),
              child: Text(
                item.label,
                style: TextStyle(
                  fontWeight:
                      item.isTotal ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            )
            : SizedBox.shrink(),
        (showing)
            ? Padding(
              padding: const EdgeInsets.only(top: paddingSmall),
              child: Text(
                item.value,
                textAlign: TextAlign.end,
                style: TextStyle(
                  fontWeight:
                      item.isTotal ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            )
            : SizedBox.shrink(),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Periode
        Obx(
          () => Container(
            width: double.infinity,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(radiusSmall),
              color: Get.isDarkMode ? kColorGlobalBgDarkBlue : kLine,
            ),
            padding: const EdgeInsets.all(paddingSmall),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  controller.periode.value,
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
                ),
                const SizedBox(width: paddingSmall),
                if (controller.periodeTanggal.value != '')
                  Text('(${controller.periodeTanggal.value})'),
              ],
            ),
          ),
        ),
        const SizedBox(height: paddingMedium),

        // Estimasi Komisi Berjalan
        Obx(() {
          if (controller.isLoading.value) {
            return const Center(
              child: Padding(
                padding: EdgeInsets.all(paddingMedium),
                child: CircularProgressIndicator(),
              ),
            );
          }

          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'widget_compensation_ongoing_estimate'.tr,
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: paddingSmall),

              // Dynamic Table
              Obx(
                () => Table(
                  columnWidths: const {
                    0: FlexColumnWidth(2),
                    1: FlexColumnWidth(1),
                  },
                  children: [
                    // Generate rows for each item
                    ...controller.items.map((item) => _buildTableRow(item)),
                    // Total row
                    _buildTableRow(
                      KompensasiItemModel(
                        label: 'Total',
                        value: controller.totalValue.value,
                        isTotal: true,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          );
        }),
        const SizedBox(height: paddingMedium),

        // Lihat Detail
        Column(
          spacing: paddingSmall,
          children: [
            if (controller.isShowingDetailsButton())
              SizedBox(
                width: double.infinity,
                child: PdlButton(
                  title: "show_details_str".tr,
                  onPressed: () => Get.toNamed(Routes.KOMPENSASI),
                ),
              ),
            const SizedBox(height: paddingSmall),
            TextButton(
              onPressed: () => controller.refreshData(),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.refresh),
                  const SizedBox(width: paddingSmall),
                  Text('refresh_str'.tr),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }
}
