import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/models/commission_model.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../models/kompensasi_item_model.dart';
import 'package:pdl_superapp/controllers/lazy_loading_controller.dart';
import 'dart:async';

class KompensasiController extends BaseControllers {
  final RxBool isExpanded = true.obs;
  final RxString periode = ''.obs;
  final RxString periodeTanggal = ''.obs;
  final RxList<KompensasiItemModel> items = <KompensasiItemModel>[].obs;
  final RxString totalValue = 'Rp0'.obs;
  String level = '';
  late SharedPreferences prefs;

  Rx<CommissionModel?> commissionData = Rx<CommissionModel?>(null);

  // Flag untuk lazy loading
  bool _hasInitialized = false;

  // StreamSubscription untuk lazy loading
  StreamSubscription<WidgetLoadEvent>? _loadEventSubscription;

  @override
  Future<void> onInit() async {
    super.onInit();
    prefs = await SharedPreferences.getInstance();
    level = prefs.getString(kStorageUserLevel) ?? '';

    final LazyLoadingController lazyLoadingController =
        Get.find<LazyLoadingController>();
    setupLazyLoading(lazyLoadingController);
    // Tidak langsung fetch data, tunggu trigger dari lazy loading
    _hasInitialized = true;
  }

  /// Method untuk setup lazy loading listener
  void setupLazyLoading(LazyLoadingController lazyLoadingController) {
    // Listen untuk load events dari LazyLoadingController
    try {
      _loadEventSubscription = lazyLoadingController.loadEventStream.listen((
        event,
      ) {
        if (event.widgetKey == 'kompensasi_widget') {
          initializeLazyLoading();
        }
      });
    } catch (e) {
      // LazyLoadingController belum ada, fallback ke loading biasa
      initializeLazyLoading();
    }
  }

  /// Method untuk trigger lazy loading dari LazyLoadingController
  Future<void> initializeLazyLoading() async {
    if (!_hasInitialized) {
      await _initialize();
    }
    load();
  }

  @override
  void onClose() {
    _loadEventSubscription?.cancel();
    super.onClose();
  }

  /// Private initialization method
  Future<void> _initialize() async {
    prefs = await SharedPreferences.getInstance();
    level = prefs.getString(kStorageUserLevel) ?? '';
    _hasInitialized = true;
  }

  bool isShowingField(String label) {
    if (level == 'BP' &&
        (label.toLowerCase() == 'override' ||
            label.toLowerCase() == 'bonus generasi')) {
      return false;
    } else if (level == 'BM' &&
        (label.toLowerCase() == 'bonus referensi agen' ||
            label.toLowerCase() == 'bonus generasi')) {
      return false;
    } else if (level == 'BD' && label.toLowerCase() == 'bonus referensi agen') {
      return false;
    }

    return true;
  }

  bool isShowingDetailsButton() {
    if (["BO", "ASM"].contains(level)) {
      return false;
    }
    return true;
  }

  void toggleExpanded() {
    isExpanded.value = !isExpanded.value;
  }

  @override
  load() async {
    setLoading(true);
    await api.getCommission(controllers: this, code: kReqGetCommission);
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );

    if (requestCode == kReqGetCommission) {
      parseCommissionData(response);
    }
  }

  @override
  void loadFailed({required int requestCode, required Response response}) {
    super.loadFailed(requestCode: requestCode, response: response);
    setLoading(false);
  }

  void parseCommissionData(dynamic response) {
    if (response == null) return;

    try {
      commissionData.value = CommissionModel.fromJson(response);

      // Update UI data
      updateUIFromCommissionData();
    } catch (e) {
      // Log error
      loadError(e);
    }
  }

  void updateUIFromCommissionData() {
    if (commissionData.value == null) return;

    final data = commissionData.value!;

    // Set period information
    String monthName = getMonthName(data.month);
    periode.value = 'period_str'.tr;
    //  ${data.periode} $monthName ${data.year}
    if (data.periode != '') {
      periode.value += ' ${data.periode}';
    }
    if (monthName != '') {
      periode.value += ' $monthName';
    }
    if (data.year != 0) {
      periode.value += ' ${data.year}';
    }

    periodeTanggal.value = data.periodDate;

    // Clear existing items
    items.clear();

    // Add commission items
    for (var commission in data.commissions) {
      String labels = commission.name;
      switch (commission.name.toLowerCase()) {
        case 'komisi dasar':
          labels = 'widget_commision_basic_str'.tr;
          break;
        case 'bonus generasi':
          labels = 'widget_commission_generation_str'.tr;
          break;
        default:
      }
      items.add(
        KompensasiItemModel(
          label: labels,
          value: formatCurrency(commission.amount.toInt()),
        ),
      );
    }

    // Update total
    totalValue.value = formatCurrency(data.totalCommission.toInt());
  }

  String getMonthName(int month) {
    const months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'Mei',
      'Jun',
      'Jul',
      'Agu',
      'Sep',
      'Okt',
      'Nov',
      'Des',
    ];

    if (month >= 1 && month <= 12) {
      return months[month - 1];
    }
    return '';
  }

  refreshData() async {
    load();
  }

  String formatCurrency(int value) {
    // Convert to string and add thousand separators
    String valueStr = value.toString();
    String result = '';
    int count = 0;

    for (int i = valueStr.length - 1; i >= 0; i--) {
      count++;
      result = valueStr[i] + result;
      if (count % 3 == 0 && i > 0) {
        result = '.$result';
      }
    }

    return 'Rp$result';
  }
}
