import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/models/response/detail_inbox_response.dart'
    as detailresp;
import 'package:pdl_superapp/models/response/inbox_response.dart';
import 'package:pdl_superapp/utils/keys.dart';

class DetailInboxController extends BaseControllers {
  final inbox = Rxn<InboxModel>();
  final detailInboxResp = Rxn<detailresp.DetailInboxResponse>();
  final agent = Rxn<detailresp.User>();
  RxBool isFromTrash = false.obs;

  @override
  void onInit() {
    inbox.value = Get.arguments['data'];
    isFromTrash.value = Get.arguments['isFromTrash'];
    reqDetailInbox(id: '${inbox.value?.id}');
    super.onInit();
  }

  Future<void> reqDetailInbox({required String id}) async {
    setLoading(true);
    await api.getDetailInbox(controllers: this, id: id, code: kReqDetailInbox);
  }

  Future<void> reqReadInbox() async {
    await api.postReadInbox(
      controllers: this,
      code: kReqReadInbox,
      ids: ['${inbox.value?.id}'],
    );
  }

  Future<void> reqBulkArchiveInbox() async {
    await api.postArchiveInbox(
      controllers: this,
      code: kReqArchiveInbox,
      ids: ['${inbox.value?.id}'],
    );
  }

  Future<void> reqBulkDeleteInbox() async {
    await api.deleteInbox(
      isHardDelete: isFromTrash.value,
      controllers: this,
      code: kReqDeleteInbox,
      ids: ['${inbox.value?.id}'],
    );
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );
    setLoading(false);
    switch (requestCode) {
      case kReqDetailInbox:
        detailInboxResp.value = detailresp.DetailInboxResponse.fromJson(
          response,
        );
        agent.value =
            detailInboxResp.value?.detailData?.approvalHeader?.requestBy;
      case kReqReadInbox:
        // Mark inbox as read locally
        if (inbox.value != null) {
          inbox.value!.isRead = true;
          inbox.refresh();
        }
        _showSuccessMessage('Berhasil menandai sebagai sudah dibaca');
        Get.back();
      case kReqArchiveInbox:
        _showSuccessMessage('Berhasil mengarsipkan inbox');
        Get.back();
      case kReqDeleteInbox:
        _showSuccessMessage('Berhasil menghapus inbox');
        Get.back();
      default:
    }
  }

  void _showSuccessMessage(String message) {
    Get.snackbar(
      'Berhasil',
      message,
      colorText: Colors.white,
      backgroundColor: Colors.green,
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  @override
  void loadFailed({required int requestCode, required Response response}) {
    super.loadFailed(requestCode: requestCode, response: response);
    setLoading(false);
    Get.snackbar(
      'Gagal',
      response.body['message'] ?? 'Terjadi Kesalahan harap ulangi kembali',
      colorText: Colors.white,
      backgroundColor: Colors.red,
      snackPosition: SnackPosition.BOTTOM,
    );
  }
}
