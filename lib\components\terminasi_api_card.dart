import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/models/approval_terminasi_model.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

class TerminasiApiCard extends StatelessWidget {
  final ApprovalTerminasiModel terminasi;
  final bool? fromKeagenan;
  final Function()? onTap;

  const TerminasiApiCard({
    super.key,
    required this.terminasi,
    this.onTap,
    this.fromKeagenan = false,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: Get.width,
        color: Colors.transparent,
        padding: EdgeInsets.only(
          left: paddingMedium,
          right: paddingMedium,
          top: paddingMedium,
        ),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Avatar dengan inisial nama
                CircleAvatar(
                  backgroundColor: kColorGlobalBlue,
                  child:
                      terminasi.target?.picture != null
                          ? ClipRRect(
                            borderRadius: BorderRadius.circular(30),
                            child: Utils.cachedImageWrapper(
                              terminasi.target?.picture!,
                              isFullUrl: true,
                              fit: BoxFit.cover,
                              width: 60,
                            ),
                          )
                          : Text(
                            Utils.getInitials(
                              terminasi.target?.name ?? 'Kandidat',
                            ),
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                ),
                SizedBox(width: paddingSmall),
                Expanded(
                  child: Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            if (fromKeagenan == true) Text('Terminasi'),
                            Text(
                              '${fromKeagenan == true ? '${terminasi.target?.agentLevel} ' : ''}${terminasi.target?.name ?? 'Kandidat'}',
                              style: Theme.of(context).textTheme.bodyLarge
                                  ?.copyWith(fontWeight: FontWeight.w700),
                            ),

                            RichText(
                              text: TextSpan(
                                children: [
                                  // TextSpan(
                                  //   text: terminasi.branch?.branchName ?? '-',
                                  // ),
                                  // if (terminasi.target?.agentCode != null)
                                  //   TextSpan(text: ' - '),
                                  if (terminasi.target?.agentCode != null)
                                    TextSpan(
                                      text: terminasi.target?.agentCode ?? '-',
                                    ),
                                ],
                                style: Theme.of(
                                  context,
                                ).textTheme.bodyMedium?.copyWith(
                                  color:
                                      Get.isDarkMode
                                          ? kColorTextTersier
                                          : kColorTextTersierLight,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                            SizedBox(height: paddingSmall),
                            Text(
                              terminasi.status ?? '',
                              style: Theme.of(
                                context,
                              ).textTheme.bodySmall?.copyWith(
                                color: kColorGlobalBlue,
                                fontWeight: FontWeight.w500,
                              ),
                            ),

                            // Utils.getTrxStatus(
                            //   context,
                            //   (terminasi.approvalStatus ?? '') == 'TERTUNDA'
                            //       ? 'DIKEMBALIKAN'
                            //       : terminasi.approvalStatus ?? '',
                            // ),

                            // Utils.getApprovalStatus(
                            //   context,
                            //   (terminasi.approvalHeader?.approvalStatus ??
                            //               'DRAFT') ==
                            //           'TERTUNDA'
                            //       ? 'DIKEMBALIKAN'
                            //       : terminasi
                            //               .approvalHeader
                            //               ?.approvalStatus ??
                            //           'DRAFT',
                            // ),
                          ],
                        ),
                      ),
                      CircleAvatar(
                        backgroundColor:
                            Get.isDarkMode
                                ? kColorGlobalBgDarkBlue
                                : kColorGlobalBgBlue,
                        child: Icon(
                          Icons.chevron_right_outlined,
                          color: kColorGlobalBlue,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            SizedBox(height: paddingSmall),
            Divider(),
          ],
        ),
      ),
    );
  }
}
