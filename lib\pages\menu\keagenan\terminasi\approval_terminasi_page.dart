import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:pdl_superapp/base/base_detail_page.dart';
import 'package:pdl_superapp/components/pdl_button.dart';
import 'package:pdl_superapp/components/pdl_drop_down.dart';
import 'package:pdl_superapp/components/pdl_text_field.dart';
import 'package:pdl_superapp/components/status_label.dart';
import 'package:pdl_superapp/controllers/menu/keagenan/terminasi/approval_terminasi_controller.dart';
import 'package:pdl_superapp/models/body/approval_body.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

class ApprovalTerminasiPage extends StatelessWidget {
  ApprovalTerminasiPage({super.key});

  final ApprovalTerminasiController controller = Get.put(
    ApprovalTerminasiController(),
    tag: Utils.getRandomString(),
  );

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        BaseDetailPage(
          title: 'title_termination'.tr,
          controller: controller,
          onRefresh: () async => controller.refreshData(),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: paddingMedium),
            width: double.infinity,
            child: SingleChildScrollView(
              child: Obx(() {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(height: paddingLarge),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          'title_termination'.tr,
                          style: Theme.of(
                            context,
                          ).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                            fontSize: 20,
                          ),
                        ),
                        Container(
                          padding: EdgeInsets.symmetric(
                            vertical: 4,
                            horizontal: 8,
                          ),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(50),
                            color: Color(0xFFF0F8FF),
                          ),
                          child: Obx(
                            () => Text(
                              controller.terminationStatus.value,
                              style: Theme.of(
                                context,
                              ).textTheme.bodySmall?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: kColorGlobalBlue,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 25),
                    _avatar(),
                    SizedBox(height: paddingMedium),
                    _infoKeagenan(context),
                    SizedBox(height: paddingLarge),
                    _pengalihanPolis(context),
                    SizedBox(height: paddingLarge),
                    const Divider(height: 1.0, color: kColorBorderLight),
                    SizedBox(height: paddingMedium),
                    _alasanTerminasi(context),
                    SizedBox(height: paddingLarge),
                    const Divider(height: 1.0, color: kColorBorderLight),
                    SizedBox(height: paddingLarge),
                    _approvalTerminasi(context),
                    SizedBox(height: paddingLarge),
                    _approvalMonitoring(context),
                    SizedBox(height: 100),
                  ],
                );
              }),
            ),
          ),
        ),
        Obx(
          () => Visibility(
            visible: true,
            child: Positioned(
              bottom: 0,
              right: 0,
              left: 0,
              child: _buttonSubmit(context),
            ),
          ),
        ),
      ],
    );
  }

  Widget _approvalTerminasi(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: Get.width,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(radiusSmall),
            border: Border.all(
              color: Get.isDarkMode ? kColorBorderDark : kColorBorderLight,
            ),
          ),
          child: Column(
            children: [
              Container(
                width: Get.width,
                padding: EdgeInsets.all(paddingSmall),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(radiusSmall),
                    topRight: Radius.circular(radiusSmall),
                  ),
                  color: Get.isDarkMode ? kColorBorderDark : kColorBorderLight,
                ),
                child: Text(
                  'Status Persetujuan',
                  style: Theme.of(
                    context,
                  ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.bold),
                ),
              ),
              SizedBox(height: paddingSmall),
              Padding(
                padding: EdgeInsetsGeometry.all(paddingSmall),
                child: PdlDropDown(
                  title: 'Status Persetujuan',
                  item: ['Setuju', 'Tolak'],
                  enabled: true,
                  selectedItem:
                      controller.selectedStatusApproval.value != ''
                          ? controller.selectedStatusApproval.value
                          : null,
                  disableSearch: true,
                  onChanged: (val) {
                    controller.selectedStatusApproval.value = val!;
                    controller.setHasChange();
                  },
                ),
              ),
              SizedBox(height: paddingSmall),
              Padding(
                padding: EdgeInsetsGeometry.all(paddingSmall),
                child: _pengalihanPolisInput(context),
              ),
              SizedBox(height: paddingSmall),
              Padding(
                padding: EdgeInsetsGeometry.all(paddingSmall),
                child: PdlTextField(
                  hint: 'Tambah catatan atau keterangan',
                  textController: controller.remarksApproval,
                  label:
                      'Catatan ${controller.selectedStatusApproval.value != 'Tolak' ? '(Opsional)' : ''}',
                  maxLength: 200,
                  height: 100,
                  isTextArea: true,
                  borderColor:
                      controller.selectedStatusApproval.value == 'Tolak'
                          ? kColorError
                          : null,
                  errorText:
                      controller.selectedStatusApproval.value == 'Tolak'
                          ? 'Wajib diisi'
                          : null,
                  keyboardType: TextInputType.multiline,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Column _pengalihanPolisInput(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'title_policy_transfer'.tr,
          style: Theme.of(
            context,
          ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.normal),
        ),
        SizedBox(height: paddingMedium),
        _TextfieldPengalihanPolis(controller: controller),
      ],
    );
  }

  Column _pengalihanPolis(BuildContext context) => Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Text(
        'title_policy_transfer'.tr,
        style: Theme.of(
          context,
        ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
      ),
      SizedBox(height: paddingMedium),
      _rowData(
        context,
        title: 'label_form_agent_code'.tr,
        content: controller.policyTranferAssignedAgentCode.value,
      ),
      SizedBox(height: paddingMedium),
      _rowData(
        context,
        title: 'label_form_full_name'.tr,
        content: controller.policyTranferAssignedTo.value,
      ),
    ],
  );

  Future<void> _approvalHistory(BuildContext context) async {
    await showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      useSafeArea: true,
      builder:
          (context) => Container(
            padding: EdgeInsets.fromLTRB(16, 24, 16, 40),
            width: double.infinity,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: const EdgeInsets.only(bottom: 28),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        "title_approval_history".tr,
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          fontSize: 20,
                        ),
                      ),
                      InkWell(
                        onTap: () => Get.back(),
                        child: Utils.cachedSvgWrapper(
                          'icon/ic-linear-x-close.svg',
                          color:
                              Get.isDarkMode ? kColorTextDark : kColorTextLight,
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 12),
                _listApprovalHistory(
                  context,
                  length: controller.approvalDetails.length,
                ),
              ],
            ),
          ),
    );
  }

  Widget _listApprovalHistory(BuildContext context, {required int length}) {
    return ListView.builder(
      shrinkWrap: true,
      itemCount: length,
      itemBuilder: (context, index) {
        final isLast = index == length - 1;
        return IntrinsicHeight(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Column(
                children: [
                  // Titik
                  Container(
                    width: 16,
                    height: 16,
                    decoration: BoxDecoration(
                      color: Color(0xFF0075BD),
                      shape: BoxShape.circle,
                      border: Border.all(color: Color(0xFFE0F1FE)),
                    ),
                  ),
                  Expanded(child: Container(width: 2, color: Colors.grey[300])),

                  if (isLast)
                    Container(
                      width: 16,
                      height: 16,
                      decoration: BoxDecoration(
                        color: Color(0xFF888888),
                        shape: BoxShape.circle,
                        border: Border.all(color: Color(0xFFE7E7E7)),
                      ),
                    ),
                ],
              ),
              SizedBox(width: paddingSmall),
              Expanded(
                child: Padding(
                  padding:
                      isLast
                          ? EdgeInsets.zero
                          : const EdgeInsets.only(bottom: paddingMedium),
                  child: _ApprovalHistoryItem(
                    approvalDetailName:
                        controller.approvalDetails[index]['actionBy']['name'],
                    approvalDetailStatus:
                        controller.approvalDetails[index]['approvalStatus'],
                    approvalDetailDate:
                        controller.approvalDetails[index]['createdAt'],
                    approvalDetailRemark:
                        controller.approvalDetails[index]['remarks'],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buttonSubmit(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Color.fromRGBO(149, 157, 165, 0.2),
            blurRadius: 24,
            spreadRadius: 0,
            offset: Offset(0, 2),
          ),
        ],
      ),
      padding: EdgeInsets.all(paddingLarge),
      child: PdlButton(
        controller: controller,
        onPressed:
            controller.hasChanges.value
                ? () => controller.submitApprovalTerminasi(
                  trxId: controller.terminationId.value,
                  body: ApprovalBody(
                    approvalHeaderId:
                        controller.terminationApprovalHeaderId.value,
                    action:
                        controller.selectedStatusApproval.value == 'Setuju'
                            ? StatusApproval.DISETUJUI.name
                            : controller.selectedStatusApproval.value == 'Tolak'
                            ? StatusApproval.DITOLAK.name
                            : StatusApproval.TERTUNDA.name,
                    remarks: controller.remarksApproval.text,
                  ),
                )
                : null,
        title: 'Submit',
      ),
    );
  }

  Container _avatar() {
    return Container(
      width: double.infinity,
      height: 358,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Colors.grey,
      ),
    );
  }

  Column _approvalMonitoring(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              'title_approval_monitoring'.tr,
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            TextButton(
              onPressed: () => _approvalHistory(context),
              child: Text(
                'label_view_history'.tr,
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: kColorGlobalBlue,
                ),
              ),
            ),
          ],
        ),
        SizedBox(height: paddingMedium),
        ListView.separated(
          shrinkWrap: true,
          padding: EdgeInsets.zero,
          physics: const NeverScrollableScrollPhysics(),
          itemBuilder:
              (context, index) => _ApprovalItem(
                approvalDetailName:
                    controller.approvalDetails[index]['actionBy']['name'],
                approvalDetailtatus:
                    controller.approvalDetails[index]['approvalStatus'],
              ),
          separatorBuilder:
              (context, index) => Padding(
                padding: const EdgeInsets.symmetric(vertical: paddingSmall),
                child: Icon(
                  Icons.keyboard_arrow_down_sharp,
                  color: Color(0xFF888888),
                ),
              ),
          itemCount: controller.approvalDetails.length,
        ),
        SizedBox(height: 60),
      ],
    );
  }

  Widget _alasanTerminasi(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'title_termination_reason'.tr,
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
        ),
        SizedBox(height: paddingMedium),
        Text(
          controller.reason.value,
          style: Theme.of(context).textTheme.titleSmall,
        ),
      ],
    );
  }

  Widget _infoKeagenan(BuildContext context) {
    return Obx(() {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'title_agency_information'.tr,
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
          ),
          SizedBox(height: paddingMedium),
          _rowData(
            context,
            title: 'label_form_agent_code'.tr,
            content: controller.agentCode.value,
          ),
          SizedBox(height: paddingMedium),
          _rowData(
            context,
            title: 'label_form_full_name'.tr,
            content: controller.agentName.value,
          ),
          SizedBox(height: paddingMedium),
          _rowData(
            context,
            title: 'label_current_level'.tr,
            content: controller.level.value,
          ),
        ],
      );
    });
  }

  Widget _rowData(
    BuildContext context, {
    required String title,
    required String content,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 150,
          child: Text(title, style: Theme.of(context).textTheme.bodyMedium),
        ),
        Text(': ', style: Theme.of(context).textTheme.bodyMedium),
        SizedBox(width: paddingSmall),
        Expanded(
          flex: 3,
          child: Text(content, style: Theme.of(context).textTheme.bodyMedium),
        ),
      ],
    );
  }
}

class _ApprovalItem extends StatelessWidget {
  const _ApprovalItem({
    required this.approvalDetailName,
    required this.approvalDetailtatus,
  });

  final String approvalDetailName;
  final String approvalDetailtatus;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Color(0xFFD1D1D1)),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(width: 44, height: 44, child: CircleAvatar()),
          SizedBox(width: paddingSmall),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  approvalDetailName,
                  style: Theme.of(
                    context,
                  ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold),
                ),
                Container(
                  padding: EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(50),
                    color: Color(0xFFF0F8FF),
                  ),
                  child: Text(
                    approvalDetailtatus,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: kColorGlobalBlue,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _ApprovalHistoryItem extends StatelessWidget {
  const _ApprovalHistoryItem({
    required this.approvalDetailName,
    required this.approvalDetailStatus,
    required this.approvalDetailDate,
    required this.approvalDetailRemark,
  });

  final String approvalDetailName;
  final String approvalDetailStatus;
  final String approvalDetailDate;
  final String approvalDetailRemark;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Color(0xFFD1D1D1)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                DateFormat(
                  'dd/MM/yyyy HH:mm',
                ).format(DateTime.parse(approvalDetailDate)),
                style: Theme.of(context).textTheme.labelLarge,
              ),
              Container(
                padding: EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(50),
                  color: Color(0xFFF0F8FF),
                ),
                child: Text(
                  approvalDetailStatus,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: kColorGlobalBlue,
                  ),
                ),
              ),
            ],
          ),
          Text(
            approvalDetailName,
            style: Theme.of(
              context,
            ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold),
          ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text("Catatan", style: Theme.of(context).textTheme.labelLarge),
              Text(': ', style: Theme.of(context).textTheme.labelLarge),
              SizedBox(width: paddingSmall),
              Expanded(
                flex: 3,
                child: Text(
                  approvalDetailRemark,
                  style: Theme.of(context).textTheme.labelLarge,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class DialogTerminasiConfirmContent extends StatelessWidget {
  final Function() onTap;
  final String? textButtonPos;
  final Widget? icon;
  final ApprovalTerminasiController controller;

  const DialogTerminasiConfirmContent({
    super.key,
    required this.onTap,
    this.textButtonPos,
    this.icon,
    required this.controller,
  });

  TextFormField _textFieldAlasan(BuildContext context) {
    return TextFormField(
      controller: controller.alasanController,
      style: Theme.of(context).textTheme.bodyMedium,
      maxLines: null,
      minLines: 3,
      maxLength: 200,
      decoration: InputDecoration(
        hintText: '',
        hintStyle: Theme.of(
          context,
        ).textTheme.bodyMedium?.copyWith(color: kColorTextTersier),
        fillColor: Colors.transparent,
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: Theme.of(context).colorScheme.primary),
        ),
        disabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(
            color: (Get.isDarkMode ? kColorBorderDark : kColorBorderLight),
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(
            color: (Get.isDarkMode ? kColorBorderDark : kColorBorderLight),
          ),
        ),
      ),
    );
  }

  Widget _cancelReason(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        RichText(
          text: TextSpan(
            children: [
              WidgetSpan(
                alignment: PlaceholderAlignment.middle,
                child: Text(
                  'title_cancel_termination'.tr,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.normal,
                  ),
                ),
              ),
            ],
          ),
        ),
        // Text(
        //   'Alasan Terminasi',
        //   style: Theme.of(
        //     context,
        //   ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
        // ),
        SizedBox(height: paddingMedium),
        _textFieldAlasan(context),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: Get.width,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(height: paddingSmall),
          SizedBox(
            width: 60,
            height: 50,
            child:
                icon ?? Utils.cachedSvgWrapper('icon/ic-dialog-question.svg'),
          ),
          SizedBox(height: paddingMedium),
          SizedBox(
            width: Get.width,
            child: Text(
              'label_cancel_application'.tr,
              textAlign: TextAlign.center,
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
          ),
          SizedBox(height: paddingMedium),
          _cancelReason(context),
          SizedBox(
            width: Get.width,
            child: Row(
              children: [
                Expanded(
                  child: FilledButton(
                    style: ButtonStyle(
                      backgroundColor: WidgetStateProperty.all(
                        Colors.transparent,
                      ),
                      side: WidgetStateProperty.all(
                        BorderSide(color: Color(0xFFD1D1D1)),
                      ),
                      foregroundColor: WidgetStateProperty.all(
                        Color(0xFF0C9DEB),
                      ),
                      padding: WidgetStateProperty.all(
                        EdgeInsets.symmetric(horizontal: paddingSmall),
                      ),
                    ),
                    child: Text(
                      'label_cancel'.tr,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                    onPressed: () => Get.back(),
                  ),
                ),
                SizedBox(width: paddingMedium),
                Expanded(
                  child: FilledButton(
                    onPressed: onTap,
                    child: Text(textButtonPos ?? 'label_yes'.tr),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _TextfieldPengalihanPolis extends StatelessWidget {
  const _TextfieldPengalihanPolis({required this.controller});
  final ApprovalTerminasiController controller;

  @override
  Widget build(BuildContext context) {
    return RawAutocomplete<String>(
      textEditingController: controller.polisTextController,
      focusNode: FocusNode(),
      optionsBuilder: (TextEditingValue textEditingValue) {
        if (textEditingValue.text == '') {
          return const Iterable<String>.empty();
        }
        return controller.dropdownOptions.entries
            .where((entry) {
              return entry.value.toLowerCase().contains(
                textEditingValue.text.toLowerCase(),
              );
            })
            .map((entry) => entry.key);
      },
      displayStringForOption: (String option) {
        return controller.dropdownOptions[option] ?? '';
      },
      onSelected: (String selection) {
        controller.selectedValue.value = selection;
        controller.polisTextController.text =
            controller.dropdownOptions[controller.selectedValue.value] ?? '';
      },
      fieldViewBuilder: (context, textController, focusNode, onFieldSubmitted) {
        return TextFormField(
          controller: textController,
          focusNode: focusNode,

          decoration: InputDecoration(
            fillColor: Colors.transparent,
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            prefixIcon: Padding(
              padding: EdgeInsets.all(paddingMedium),
              child: Utils.cachedSvgWrapper(
                'icon/ic-linear-search -2.svg',
                color: Get.isDarkMode ? kColorTextDark : kColorTextLight,
              ),
            ),
            suffixIcon: InkWell(
              onTap: () {
                textController.text = '';
                focusNode.requestFocus();
              },
              child: Padding(
                padding: EdgeInsets.all(paddingMedium),
                child: Utils.cachedSvgWrapper(
                  'icon/ic-linear-x-close.svg',
                  color: Get.isDarkMode ? kColorTextDark : kColorTextLight,
                ),
              ),
            ),

            disabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: (Get.isDarkMode ? kColorBorderDark : kColorBorderLight),
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: (Get.isDarkMode ? kColorBorderDark : kColorBorderLight),
              ),
            ),
          ),
        );
      },
      optionsViewBuilder: (context, onSelected, options) {
        return Material(
          elevation: 4,
          child: ListView.builder(
            padding: EdgeInsets.zero,
            shrinkWrap: true,
            itemCount: options.length,
            itemBuilder: (context, index) {
              final option = options.elementAt(index);
              final id = options.elementAt(index);
              final name = controller.dropdownOptions[id] ?? '';
              return ListTile(
                title: Text('($id) $name'),
                onTap: () => onSelected(option),
              );
            },
          ),
        );
      },
    );
  }
}
